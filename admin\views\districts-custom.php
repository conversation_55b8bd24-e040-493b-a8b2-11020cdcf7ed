<?php
/**
 * Provincial Administration Manager - Custom Districts Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get districts controller instance
$districts_controller = Provincial_Districts_Controller::get_instance();

// Check user type and get appropriate districts
$current_user_id = get_current_user_id();
$user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
$is_district_user = ($user_type === 'district');
$is_provincial_user = Provincial_User_Roles::user_has_provincial_access($current_user_id);
$is_admin_user = current_user_can('manage_options') || current_user_can('administrator');

// Get districts based on user permissions
if ($is_district_user && !$is_admin_user) {
    // District users see only their assigned districts
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

    if (!empty($assigned_districts)) {
        $districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => -1,
            'post_status' => 'any',
            'post__in' => $assigned_districts,
            'orderby' => 'title',
            'order' => 'ASC'
        ));
    } else {
        $districts = array(); // No assigned districts
    }
} else {
    // Provincial users and administrators see all districts
    $districts = get_posts(array(
        'post_type' => 'esp_district',
        'numberposts' => -1,
        'post_status' => 'any',
        'orderby' => 'title',
        'order' => 'ASC'
    ));
}

// Get district names for display (for district users)
$district_names = array();
if ($is_district_user && !$is_admin_user) {
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
    foreach ($assigned_districts as $district_id) {
        $district = get_post($district_id);
        if ($district && $district->post_status === 'publish') {
            $district_names[] = $district->post_title;
        }
    }
}
?>

<div class="esp-custom-admin-wrap">
    <!-- Header Section -->
    <div class="esp-admin-header">
        <div class="esp-header-content">
            <h1 class="esp-page-title">
                <span class="esp-icon">🏛️</span>
                <?php if ($is_district_user && !$is_admin_user): ?>
                    <?php _e('My Districts', 'esp-admin-manager'); ?>
                <?php else: ?>
                    <?php _e('Districts Management', 'esp-admin-manager'); ?>
                <?php endif; ?>
            </h1>
            <p class="esp-page-description">
                <?php if ($is_district_user && !$is_admin_user): ?>
                    <?php _e('View and manage your assigned districts', 'esp-admin-manager'); ?>
                <?php else: ?>
                    <?php _e('Manage all districts, their information, and administrative details', 'esp-admin-manager'); ?>
                <?php endif; ?>
            </p>
        </div>
        <?php if ($is_admin_user): ?>
        <div class="esp-header-actions">
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts&action=create'); ?>" class="esp-btn esp-btn-primary">
                <span class="esp-btn-icon">➕</span>
                <?php _e('Add New District', 'esp-admin-manager'); ?>
            </a>
        </div>
        <?php endif; ?>
    </div>

    <!-- District Info for District Users -->
    <?php if ($is_district_user && !$is_admin_user && !empty($district_names)): ?>
        <div class="esp-info-banner">
            <div class="esp-info-content">
                <span class="esp-info-icon">ℹ️</span>
                <div class="esp-info-text">
                    <strong><?php _e('Assigned Districts:', 'esp-admin-manager'); ?></strong>
                    <?php echo esc_html(implode(', ', $district_names)); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Districts Grid -->
    <div class="esp-content-section">
        <?php if (!empty($districts)): ?>
            <div class="esp-districts-grid">
                <?php foreach ($districts as $district):
                    $llgs = get_post_meta($district->ID, '_esp_district_llgs', true);
                    $wards = get_post_meta($district->ID, '_esp_district_wards', true);
                    $population = get_post_meta($district->ID, '_esp_district_population', true);
                    $area = get_post_meta($district->ID, '_esp_district_area', true);
                    $page_url = get_post_meta($district->ID, '_esp_district_page_url', true);
                    $photo_url = get_the_post_thumbnail_url($district->ID, 'medium');
                ?>
                    <div class="esp-district-card" data-district-id="<?php echo esc_attr($district->ID); ?>">
                        <div class="esp-district-photo">
                            <?php if ($photo_url): ?>
                                <img src="<?php echo esc_url($photo_url); ?>" alt="<?php echo esc_attr($district->post_title); ?>">
                            <?php else: ?>
                                <div class="esp-district-placeholder">
                                    <span class="esp-placeholder-icon">🏛️</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="esp-district-info">
                            <h3 class="esp-district-name"><?php echo esc_html($district->post_title); ?></h3>
                            
                            <?php if ($district->post_content): ?>
                                <div class="esp-district-description">
                                    <?php echo esc_html(wp_trim_words($district->post_content, 15)); ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="esp-district-stats">
                                <?php if ($llgs): ?>
                                    <div class="esp-stat-item">
                                        <span class="esp-stat-value"><?php echo esc_html($llgs); ?></span>
                                        <span class="esp-stat-label"><?php _e('LLGs', 'esp-admin-manager'); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($wards): ?>
                                    <div class="esp-stat-item">
                                        <span class="esp-stat-value"><?php echo esc_html($wards); ?></span>
                                        <span class="esp-stat-label"><?php _e('Wards', 'esp-admin-manager'); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($population): ?>
                                    <div class="esp-stat-item">
                                        <span class="esp-stat-value"><?php echo esc_html(number_format($population)); ?></span>
                                        <span class="esp-stat-label"><?php _e('Population', 'esp-admin-manager'); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($area): ?>
                                    <div class="esp-stat-item">
                                        <span class="esp-stat-value"><?php echo esc_html($area); ?></span>
                                        <span class="esp-stat-label"><?php _e('km²', 'esp-admin-manager'); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="esp-district-status">
                                <span class="esp-status esp-status-<?php echo esc_attr($district->post_status); ?>">
                                    <?php echo esc_html(ucfirst($district->post_status)); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="esp-district-actions">
                            <?php if ($page_url): ?>
                                <a href="<?php echo esc_url($page_url); ?>" target="_blank"
                                   class="esp-btn esp-btn-primary esp-btn-sm"
                                   title="<?php _e('Visit district page', 'esp-admin-manager'); ?>">
                                    <span class="esp-btn-icon">🔗</span>
                                    <?php _e('Visit Page', 'esp-admin-manager'); ?>
                                </a>
                            <?php endif; ?>

                            <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts&action=edit&district_id=' . $district->ID); ?>"
                               class="esp-btn esp-btn-secondary esp-btn-sm">
                                <span class="esp-btn-icon">✏️</span>
                                <?php _e('Edit', 'esp-admin-manager'); ?>
                            </a>

                            <?php if ($is_admin_user): ?>
                                <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts&action=delete&district_id=' . $district->ID); ?>"
                                   class="esp-btn esp-btn-danger esp-btn-sm"
                                   onclick="return confirm('<?php _e('Are you sure you want to delete this district?', 'esp-admin-manager'); ?>')">
                                    <span class="esp-btn-icon">🗑️</span>
                                    <?php _e('Delete', 'esp-admin-manager'); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="esp-empty-state">
                <div class="esp-empty-icon">🏛️</div>
                <h3 class="esp-empty-title">
                    <?php if ($is_district_user && !$is_admin_user): ?>
                        <?php _e('No districts assigned', 'esp-admin-manager'); ?>
                    <?php else: ?>
                        <?php _e('No districts found', 'esp-admin-manager'); ?>
                    <?php endif; ?>
                </h3>
                <p class="esp-empty-description">
                    <?php if ($is_district_user && !$is_admin_user): ?>
                        <?php _e('No districts have been assigned to your account yet. Please contact your administrator.', 'esp-admin-manager'); ?>
                    <?php else: ?>
                        <?php _e('Get started by adding your first district using the "Add New District" button above.', 'esp-admin-manager'); ?>
                    <?php endif; ?>
                </p>
                <?php if ($is_admin_user): ?>
                <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts&action=create'); ?>" class="esp-btn esp-btn-primary">
                    <span class="esp-btn-icon">➕</span>
                    <?php _e('Add Your First District', 'esp-admin-manager'); ?>
                </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Statistics Section -->
    <div class="esp-stats-section">
        <h3 class="esp-section-title"><?php _e('District Statistics', 'esp-admin-manager'); ?></h3>
        <div class="esp-stats-grid">
            <div class="esp-stat-card">
                <div class="esp-stat-icon">🏛️</div>
                <div class="esp-stat-content">
                    <div class="esp-stat-number"><?php echo count($districts); ?></div>
                    <div class="esp-stat-label"><?php _e('Total Districts', 'esp-admin-manager'); ?></div>
                </div>
            </div>
            
            <div class="esp-stat-card">
                <div class="esp-stat-icon">📊</div>
                <div class="esp-stat-content">
                    <div class="esp-stat-number">
                        <?php 
                        $published_count = 0;
                        foreach ($districts as $district) {
                            if ($district->post_status === 'publish') {
                                $published_count++;
                            }
                        }
                        echo $published_count;
                        ?>
                    </div>
                    <div class="esp-stat-label"><?php _e('Published', 'esp-admin-manager'); ?></div>
                </div>
            </div>
            
            <div class="esp-stat-card">
                <div class="esp-stat-icon">👥</div>
                <div class="esp-stat-content">
                    <div class="esp-stat-number">
                        <?php 
                        $total_population = 0;
                        foreach ($districts as $district) {
                            $population = get_post_meta($district->ID, '_esp_district_population', true);
                            if ($population) {
                                $total_population += intval($population);
                            }
                        }
                        echo $total_population ? number_format($total_population) : '0';
                        ?>
                    </div>
                    <div class="esp-stat-label"><?php _e('Total Population', 'esp-admin-manager'); ?></div>
                </div>
            </div>
            
            <div class="esp-stat-card">
                <div class="esp-stat-icon">📏</div>
                <div class="esp-stat-content">
                    <div class="esp-stat-number">
                        <?php 
                        $total_area = 0;
                        foreach ($districts as $district) {
                            $area = get_post_meta($district->ID, '_esp_district_area', true);
                            if ($area) {
                                $total_area += floatval($area);
                            }
                        }
                        echo $total_area ? number_format($total_area) : '0';
                        ?>
                    </div>
                    <div class="esp-stat-label"><?php _e('Total Area (km²)', 'esp-admin-manager'); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>


