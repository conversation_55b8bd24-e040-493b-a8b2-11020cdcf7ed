# Recent News and Upcoming Events Shortcodes

## Overview

This guide documents the implementation of two new shortcodes specifically designed for displaying recent news and upcoming events in a compact format, perfect for widgets, sidebars, or homepage sections.

## New Shortcodes

### 1. Recent News Shortcode

**Purpose**: Display recent news articles with optimized defaults for compact display.

**Shortcode Names** (all produce identical output):
- `[dakoii_recent_news]` (recommended)
- `[dakoii_prov_admin_recent_news]` (full name)
- `[provincial_recent_news]` (generic)
- `[esp_recent_news]` (legacy)

**Parameters**:
- `limit` - Number of articles to show (default: 3)
- `show_images` - Display featured images (default: 'true')
- `level` - Access level (default: 'district')
- `district_id` - Filter by specific district (optional)

**Usage Examples**:
```html
<!-- Basic usage - shows 3 recent news articles -->
[dakoii_recent_news]

<!-- Show 5 recent news articles -->
[dakoii_recent_news limit="5"]

<!-- Show recent news without images -->
[dakoii_recent_news show_images="false"]

<!-- Show recent news from specific district -->
[dakoii_recent_news district_id="123"]

<!-- Combined parameters -->
[dakoii_recent_news limit="4" show_images="true" district_id="456"]
```

### 2. Upcoming Events Shortcode

**Purpose**: Display upcoming events with optimized defaults for compact display.

**Shortcode Names** (all produce identical output):
- `[dakoii_upcoming_events]` (recommended)
- `[dakoii_prov_admin_upcoming_events]` (full name)
- `[provincial_upcoming_events]` (generic)
- `[esp_upcoming_events]` (legacy)

**Parameters**:
- `limit` - Number of events to show (default: 3)
- `show_images` - Display featured images (default: 'true')
- `level` - Access level (default: 'district')
- `district_id` - Filter by specific district (optional)

**Usage Examples**:
```html
<!-- Basic usage - shows 3 upcoming events -->
[dakoii_upcoming_events]

<!-- Show 5 upcoming events -->
[dakoii_upcoming_events limit="5"]

<!-- Show upcoming events without images -->
[dakoii_upcoming_events show_images="false"]

<!-- Show upcoming events from specific district -->
[dakoii_upcoming_events district_id="123"]

<!-- Combined parameters -->
[dakoii_upcoming_events limit="4" show_images="true" district_id="456"]
```

## Key Features

### Optimized Defaults
- **Recent News**: Always shows recent articles (not featured-only)
- **Upcoming Events**: Always shows only upcoming events (not past events)
- **Compact Layout**: Smaller grid items suitable for sidebars and widgets
- **Responsive Design**: Adapts to mobile screens

### Styling
Both shortcodes use compact styling with:
- Smaller grid items (280px minimum width vs 350px for regular shortcodes)
- Reduced padding and margins
- Smaller image heights (150px vs 200px)
- Compact typography

### CSS Classes
- Recent News: `.esp-recent-news` class added for custom styling
- Upcoming Events: `.esp-upcoming-events` class added for custom styling

## Common Use Cases

### Homepage Sections
```html
<div class="row">
    <div class="col-md-6">
        <h3>Latest News</h3>
        [dakoii_recent_news limit="3"]
    </div>
    <div class="col-md-6">
        <h3>Upcoming Events</h3>
        [dakoii_upcoming_events limit="3"]
    </div>
</div>
```

### Sidebar Widgets
```html
<!-- In a sidebar widget -->
<h4>Recent News</h4>
[dakoii_recent_news limit="2" show_images="false"]

<h4>Upcoming Events</h4>
[dakoii_upcoming_events limit="2" show_images="false"]
```

### District-Specific Pages
```html
<!-- Show news and events for a specific district -->
<h2>District Updates</h2>
[dakoii_recent_news district_id="123" limit="4"]

<h2>District Events</h2>
[dakoii_upcoming_events district_id="123" limit="4"]
```

## Differences from Regular Shortcodes

| Feature | Regular Shortcodes | New Compact Shortcodes |
|---------|-------------------|------------------------|
| Default Limit | 5 items | 3 items |
| Grid Item Size | 350px minimum | 280px minimum |
| Image Height | 200px | 150px |
| Padding | 2rem sections, 1.5rem cards | 1rem sections, 1rem cards |
| Purpose | Full page display | Compact widget display |
| Parameters | More flexible options | Simplified, optimized defaults |

## Technical Implementation

### File Locations
- **Shortcode Registration**: `includes/class-provincial-shortcodes.php` (lines 163-220)
- **Shortcode Methods**: `includes/class-provincial-shortcodes.php` (lines 878-932)
- **CSS Styling**: `public/css/public-style.css` (lines 684-767, 1148-1177)

### How It Works
1. New shortcodes call existing `news_shortcode()` and `events_shortcode()` methods
2. Pass optimized parameters to ensure correct behavior
3. Add custom CSS classes for compact styling
4. Maintain all existing functionality (permissions, filtering, etc.)

## Customization

### Custom CSS
You can override the compact styling by targeting the specific classes:

```css
/* Make recent news even more compact */
.esp-recent-news .esp-news-card {
    border-radius: 5px;
    margin-bottom: 0.5rem;
}

/* Customize upcoming events colors */
.esp-upcoming-events .esp-event-card {
    border-left-color: #your-color;
}
```

### Widget Integration
These shortcodes work perfectly in WordPress widgets:
1. Add a "Custom HTML" widget
2. Insert the shortcode
3. The compact styling will automatically apply

## Troubleshooting

### Common Issues

**Shortcode not displaying**: Ensure the plugin is activated and shortcodes are properly registered.

**No content showing**: Check that news/events exist and are published.

**Permission errors**: Verify user has appropriate district access.

**Styling issues**: Clear cache and check for CSS conflicts.

### Debug Tips
- Use `[dakoii_news]` and `[dakoii_events]` to test if regular shortcodes work
- Check browser console for JavaScript errors
- Verify CSS files are loading properly

## Conclusion

These new shortcodes provide an easy way to display recent news and upcoming events in a compact format, perfect for modern website layouts that need concise, attractive content blocks.
