<?php
/**
 * Provincial Administration Manager - Create District View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check permissions - only administrators can create districts
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.', 'esp-admin-manager'));
}

// Get current user information
$current_user_id = get_current_user_id();
$user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
$is_admin_user = current_user_can('manage_options') || current_user_can('administrator');

// Form submission is handled by the admin class
?>

<div class="esp-custom-admin-wrap">
    <!-- Header Section -->
    <div class="esp-admin-header">
        <div class="esp-header-content">
            <h1 class="esp-page-title">
                <span class="esp-icon">➕</span>
                <?php _e('Add New District', 'esp-admin-manager'); ?>
            </h1>
            <p class="esp-page-description">
                <?php _e('Create a new district with all necessary information and details', 'esp-admin-manager'); ?>
            </p>
        </div>
        <div class="esp-header-actions">
            <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts'); ?>" class="esp-btn esp-btn-secondary">
                <span class="esp-btn-icon">←</span>
                <?php _e('Back to Districts', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php settings_errors('esp_messages'); ?>

    <!-- Create District Form -->
    <div class="esp-content-section">
        <div class="esp-form-container">
            <form method="post" class="esp-form" id="create-district-form">
                <?php wp_nonce_field('create_district', 'district_nonce'); ?>
                <input type="hidden" name="action" value="create_district">

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('Basic Information', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-name" class="esp-form-label">
                                <?php _e('District Name', 'esp-admin-manager'); ?> <span class="esp-required">*</span>
                            </label>
                            <input type="text" id="district-name" name="district_name" class="esp-form-input"
                                   value="<?php echo isset($_POST['district_name']) ? esc_attr($_POST['district_name']) : ''; ?>" required>
                            <p class="esp-form-help"><?php _e('Enter the official name of the district', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-description" class="esp-form-label">
                                <?php _e('Description', 'esp-admin-manager'); ?>
                            </label>
                            <textarea id="district-description" name="district_description" class="esp-form-textarea" rows="4"><?php echo isset($_POST['district_description']) ? esc_textarea($_POST['district_description']) : ''; ?></textarea>
                            <p class="esp-form-help"><?php _e('Provide a brief description of the district', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('Administrative Details', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-llgs" class="esp-form-label">
                                <?php _e('Number of LLGs', 'esp-admin-manager'); ?>
                            </label>
                            <input type="number" id="district-llgs" name="district_llgs" class="esp-form-input" min="0"
                                   value="<?php echo isset($_POST['district_llgs']) ? esc_attr($_POST['district_llgs']) : ''; ?>">
                            <p class="esp-form-help"><?php _e('Local Level Governments in this district', 'esp-admin-manager'); ?></p>
                        </div>

                        <div class="esp-form-group">
                            <label for="district-wards" class="esp-form-label">
                                <?php _e('Number of Wards', 'esp-admin-manager'); ?>
                            </label>
                            <input type="number" id="district-wards" name="district_wards" class="esp-form-input" min="0"
                                   value="<?php echo isset($_POST['district_wards']) ? esc_attr($_POST['district_wards']) : ''; ?>">
                            <p class="esp-form-help"><?php _e('Electoral wards in this district', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('Demographics & Geography', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-population" class="esp-form-label">
                                <?php _e('Population', 'esp-admin-manager'); ?>
                            </label>
                            <input type="number" id="district-population" name="district_population" class="esp-form-input" min="0"
                                   value="<?php echo isset($_POST['district_population']) ? esc_attr($_POST['district_population']) : ''; ?>">
                            <p class="esp-form-help"><?php _e('Total population of the district', 'esp-admin-manager'); ?></p>
                        </div>

                        <div class="esp-form-group">
                            <label for="district-area" class="esp-form-label">
                                <?php _e('Area (km²)', 'esp-admin-manager'); ?>
                            </label>
                            <input type="number" id="district-area" name="district_area" class="esp-form-input" min="0" step="0.01"
                                   value="<?php echo isset($_POST['district_area']) ? esc_attr($_POST['district_area']) : ''; ?>">
                            <p class="esp-form-help"><?php _e('Total area in square kilometers', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('District Page Link', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-page-url" class="esp-form-label">
                                <?php _e('District Page URL', 'esp-admin-manager'); ?>
                            </label>
                            <input type="url" id="district-page-url" name="district_page_url" class="esp-form-input"
                                   placeholder="https://example.com/district-page"
                                   value="<?php echo isset($_POST['district_page_url']) ? esc_attr($_POST['district_page_url']) : ''; ?>">
                            <p class="esp-form-help"><?php _e('Enter the URL of the page users should visit when they click on this district. Leave empty if no link is needed.', 'esp-admin-manager'); ?></p>
                        </div>
                    </div>
                </div>

                <div class="esp-form-section">
                    <h3 class="esp-section-title"><?php _e('District Photo', 'esp-admin-manager'); ?></h3>

                    <div class="esp-form-row">
                        <div class="esp-form-group">
                            <label for="district-photo" class="esp-form-label">
                                <?php _e('Featured Image', 'esp-admin-manager'); ?>
                            </label>
                            <div class="esp-media-upload">
                                <button type="button" id="upload-photo-btn" class="esp-btn esp-btn-secondary">
                                    <span class="esp-btn-icon">📷</span>
                                    <?php _e('Choose Photo', 'esp-admin-manager'); ?>
                                </button>
                                <div id="photo-preview" class="esp-photo-preview" style="display: none;">
                                    <img id="preview-image" src="" alt="Preview">
                                    <button type="button" id="remove-photo-btn" class="esp-btn esp-btn-danger esp-btn-sm">
                                        <?php _e('Remove', 'esp-admin-manager'); ?>
                                    </button>
                                </div>
                                <input type="hidden" id="district-photo-id" name="district_photo_id" value="">
                                <p class="esp-form-help"><?php _e('Upload a representative image for this district', 'esp-admin-manager'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="esp-form-actions">
                    <a href="<?php echo admin_url('admin.php?page=provincial-admin-districts'); ?>" class="esp-btn esp-btn-secondary">
                        <?php _e('Cancel', 'esp-admin-manager'); ?>
                    </a>
                    <button type="submit" name="submit_district" class="esp-btn esp-btn-primary">
                        <span class="esp-btn-icon">💾</span>
                        <?php _e('Create District', 'esp-admin-manager'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    let mediaUploader;

    // Media upload button
    $('#upload-photo-btn').on('click', function(e) {
        e.preventDefault();

        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        mediaUploader = wp.media({
            title: 'Choose District Photo',
            button: {
                text: 'Use this photo'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });

        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#district-photo-id').val(attachment.id);
            $('#preview-image').attr('src', attachment.url);
            $('#photo-preview').show();
        });

        mediaUploader.open();
    });

    // Remove photo button
    $('#remove-photo-btn').on('click', function(e) {
        e.preventDefault();
        $('#district-photo-id').val('');
        $('#photo-preview').hide();
    });
});
</script>
